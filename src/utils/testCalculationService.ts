import { CalculationService, calculateCosts } from "./calculationService";
import { ExpenseCategory } from "@/types/costCalculation";

// Test data
const sampleExpenses: ExpenseCategory[] = [
  {
    id: "1",
    name: "Monthly Rent",
    iconName: "Home",
    monthlyAmount: 15000,
    frequency: "monthly",
  },
  {
    id: "2",
    name: "Yearly Insurance",
    iconName: "Shield",
    monthlyAmount: 24000, // This represents yearly amount
    frequency: "yearly",
  },
  {
    id: "3",
    name: "Car Replacement",
    iconName: "Car",
    monthlyAmount: 500000, // This represents cost per occurrence
    frequency: "one-time",
    yearsInterval: 10,
  },
];

const testCalculationService = () => {
  console.log("Testing Calculation Service...");

  const service = new CalculationService(
    sampleExpenses,
    60, // retirement age
    25, // retirement duration
    30, // current age
    6, // inflation rate
    12 // annual return
  );

  // Test Step 1: Yearly expenses calculation
  console.log("\n=== Step 1: Yearly Expenses Breakdown ===");
  const yearlyBreakdowns = service.calculateYearlyExpenses();
  console.log(`Generated ${yearlyBreakdowns.length} yearly breakdowns`);

  // Show first year breakdown
  if (yearlyBreakdowns.length > 0) {
    const firstYear = yearlyBreakdowns[0];
    console.log("First year of retirement:", {
      year: firstYear.year,
      age: firstYear.age,
      monthlyExpenses: firstYear.monthlyExpenses,
      yearlyExpenses: firstYear.yearlyExpenses,
      oneTimeExpenses: firstYear.oneTimeExpenses,
      totalYearlyExpenses: firstYear.totalYearlyExpenses,
    });
  }

  // Test Step 2: Aggregation
  console.log("\n=== Step 2: Aggregated Results ===");
  const aggregatedResults = service.aggregateResults(yearlyBreakdowns);
  console.log("Aggregated results:", aggregatedResults);

  // Test Step 3: Cost Results
  console.log("\n=== Step 3: Cost Results ===");
  const costResults = service.generateCostResults(
    yearlyBreakdowns,
    aggregatedResults
  );
  console.log("Cost results:", {
    currentMonthlyCost: costResults.currentMonthlyCost,
    currentAnnualCost: costResults.currentAnnualCost,
    adjustedMonthlyCost: costResults.adjustedMonthlyCost,
    adjustedAnnualCost: costResults.adjustedAnnualCost,
    totalRetirementCost: costResults.totalRetirementCost,
    oneTimeCosts: costResults.oneTimeCosts,
  });

  // Test Step 4: One-time occurrences
  console.log("\n=== Step 4: One-Time Occurrences ===");
  const oneTimeOccurrences = service.generateOneTimeOccurrences();
  console.log(`Generated ${oneTimeOccurrences.length} one-time occurrences`);
  oneTimeOccurrences.forEach((occurrence, index) => {
    console.log(`Occurrence ${index + 1}:`, {
      age: occurrence.age,
      year: occurrence.year,
      expenseName: occurrence.expenseName,
      amount: occurrence.amount,
    });
  });

  // Test Step 5: Complete calculation
  console.log("\n=== Step 5: Complete Calculation ===");
  const allResults = service.calculateAll();
  console.log("All results calculated successfully:", {
    yearlyBreakdownsCount: allResults.yearlyBreakdowns.length,
    aggregatedResults: allResults.aggregatedResults,
    costResults: allResults.costResults,
    oneTimeOccurrencesCount: allResults.oneTimeOccurrences.length,
  });

  // Test utility functions
  console.log("\n=== Utility Functions ===");
  const costResultsFromUtility = calculateCosts(
    sampleExpenses,
    60,
    25,
    30,
    6,
    12
  );
  console.log("Utility function result:", costResultsFromUtility);

  console.log("\n✅ All tests completed successfully!");
};

// Export for use in development
export { testCalculationService };
